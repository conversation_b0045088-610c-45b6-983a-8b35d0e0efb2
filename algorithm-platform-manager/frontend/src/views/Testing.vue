<template>
  <div class="page-container">
    <el-row :gutter="20">
      <!-- 测试配置 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>测试配置</span>
          </template>
          
          <el-form :model="testForm" label-width="100px">
            <el-form-item label="选择容器">
              <el-select 
                v-model="testForm.containerId" 
                placeholder="请选择要测试的容器"
                style="width: 100%"
                @change="onContainerChange"
              >
                <el-option
                  v-for="container in testingContainers"
                  :key="container.id"
                  :label="`${container.name} (${container.algorithm_name})`"
                  :value="container.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="API端点">
              <el-select
                v-model="testForm.endpoint"
                placeholder="请选择API端点"
                style="width: 100%"
                :loading="loadingEndpoints"
                @change="onEndpointChange"
                :disabled="!testForm.containerId"
              >
                <el-option
                  v-for="endpoint in availableEndpoints"
                  :key="endpoint.path"
                  :label="`${endpoint.method} ${endpoint.path} - ${endpoint.description}`"
                  :value="endpoint.path"
                >
                  <div style="display: flex; justify-content: space-between;">
                    <span>{{ endpoint.path }}</span>
                    <el-tag size="small" :type="getMethodTagType(endpoint.method)">
                      {{ endpoint.method }}
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="请求方法">
              <el-select v-model="testForm.method" style="width: 100%" :disabled="!testForm.endpoint">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
            </el-form-item>

            <el-form-item label="上传文件">
              <div class="upload-section">
                <div class="upload-area">
                  <el-upload
                    ref="uploadRef"
                    :auto-upload="false"
                    :show-file-list="false"
                    :limit="1"
                    accept="image/*,video/*,.txt,.json"
                    @change="handleFileChange"
                  >
                    <el-button type="primary">
                      <el-icon><Upload /></el-icon>
                      选择文件
                    </el-button>
                    <template #tip>
                      <div class="el-upload__tip">
                        支持图片、视频、文本等格式
                        <span v-if="testForm.file" class="file-uploaded">
                          ✅ 文件已上传，可使用快速测试
                        </span>
                      </div>
                    </template>
                  </el-upload>
                </div>

                <!-- 文件预览区域 -->
                <div v-if="filePreview" class="file-preview">
                  <div class="preview-header">
                    <span class="preview-title">文件预览</span>
                    <el-button
                      size="small"
                      type="danger"
                      @click="clearFile"
                      :icon="Delete"
                    >
                      删除
                    </el-button>
                  </div>
                  <div class="preview-content">
                    <img
                      v-if="filePreview.type === 'image'"
                      :src="filePreview.url"
                      :alt="filePreview.name"
                      class="preview-image"
                    />
                    <div v-else-if="filePreview.type === 'video'" class="video-preview">
                      <video
                        :src="filePreview.url"
                        controls
                        class="preview-video"
                      />
                    </div>
                    <div v-else class="file-info">
                      <el-icon class="file-icon"><Document /></el-icon>
                      <div class="file-details">
                        <div class="file-name">{{ filePreview.name }}</div>
                        <div class="file-size">{{ formatFileSize(filePreview.size) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="请求参数">
              <div v-if="currentEndpoint && currentEndpoint.parameters && Object.keys(currentEndpoint.parameters).length > 0"
                   class="parameter-hints">
                <el-alert
                  title="参数说明"
                  type="info"
                  :closable="false"
                  style="margin-bottom: 10px;"
                >
                  <ul style="margin: 0; padding-left: 20px;">
                    <li v-for="(desc, param) in currentEndpoint.parameters" :key="param">
                      <strong>{{ param }}</strong>: {{ desc }}
                    </li>
                  </ul>
                </el-alert>
              </div>
              <el-input
                v-model="testForm.parameters"
                type="textarea"
                :rows="4"
                :placeholder="getParameterPlaceholder()"
              />
              <div style="margin-top: 5px;">
                <el-button
                  size="small"
                  @click="fillDefaultParameters"
                  :disabled="!currentEndpoint"
                >
                  填充默认参数
                </el-button>
                <el-button
                  size="small"
                  @click="clearParameters"
                >
                  清空参数
                </el-button>
              </div>
            </el-form-item>

            <el-form-item>
              <div class="test-buttons">
                <el-button
                  type="primary"
                  @click="executeTest"
                  :loading="testing"
                  :disabled="!testForm.containerId || !testForm.endpoint"
                  style="flex: 1"
                >
                  <el-icon><Connection /></el-icon>
                  执行测试
                </el-button>
                <el-tooltip
                  content="快速测试：自动使用默认参数，适合快速验证算法效果"
                  placement="top"
                >
                  <el-button
                    type="success"
                    @click="quickTest"
                    :loading="testing"
                    :disabled="!canQuickTest"
                    style="margin-left: 10px"
                  >
                    <el-icon><Lightning /></el-icon>
                    快速测试
                  </el-button>
                </el-tooltip>
              </div>
              <div class="button-tips">
                <el-text size="small" type="info">
                  💡 <strong>执行测试</strong>：使用当前配置的参数进行测试
                  <br>
                  ⚡ <strong>快速测试</strong>：自动填充默认参数并执行（需要上传文件）
                </el-text>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 测试结果 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>测试结果</span>
              <el-button 
                v-if="testResult" 
                @click="clearResult" 
                size="small"
              >
                清空结果
              </el-button>
            </div>
          </template>

          <div v-if="!testResult && !testing" class="empty-result">
            <el-empty description="请选择容器并执行测试" />
          </div>

          <div v-else-if="testing" class="testing-status">
            <el-skeleton :rows="5" animated />
            <div class="testing-text">正在执行测试...</div>
          </div>

          <div v-else class="result-content">
            <!-- 请求信息 -->
            <el-descriptions title="请求信息" :column="2" border>
              <el-descriptions-item label="容器">
                {{ selectedContainer?.name }}
              </el-descriptions-item>
              <el-descriptions-item label="算法">
                {{ selectedContainer?.algorithm_name }}
              </el-descriptions-item>
              <el-descriptions-item label="端点">
                {{ testForm.endpoint }}
              </el-descriptions-item>
              <el-descriptions-item label="方法">
                <el-tag>{{ testForm.method }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="状态码">
                <el-tag :type="testResult.status >= 400 ? 'danger' : 'success'">
                  {{ testResult.status }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="响应时间">
                {{ testResult.response_time }}ms
              </el-descriptions-item>
            </el-descriptions>

            <!-- 响应数据 -->
            <div class="response-section">
              <h4>响应数据</h4>
              <el-tabs v-model="activeTab">
                <el-tab-pane label="🎨 智能格式化" name="formatted">
                  <div class="formatted-content">
                    <pre class="json-content">{{ formatJSON(testResult.data) }}</pre>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="📄 原始JSON" name="raw">
                  <div class="raw-content">
                    <pre class="json-content raw-json">{{ getRawJSON(testResult.data) }}</pre>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="📊 数据统计" name="stats" v-if="getDataStats(testResult.data)">
                  <div class="stats-content">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item
                        v-for="(value, key) in getDataStats(testResult.data)"
                        :key="key"
                        :label="key"
                      >
                        {{ value }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="🖼️ 图片渲染" name="render" v-if="shouldShowImageRender">
                  <div class="render-content">
                    <div v-if="!filePreview || filePreview.type !== 'image'" class="no-image-tip">
                      <el-empty description="请上传图片文件以查看渲染结果" />
                    </div>
                    <div v-else class="image-render-container">
                      <div class="render-controls">
                        <el-space>
                          <el-button
                            size="small"
                            @click="toggleDetectionBoxes"
                            :type="showDetectionBoxes ? 'primary' : 'default'"
                          >
                            {{ showDetectionBoxes ? '隐藏' : '显示' }}检测框
                          </el-button>
                          <el-button
                            size="small"
                            @click="toggleLabels"
                            :type="showLabels ? 'primary' : 'default'"
                          >
                            {{ showLabels ? '隐藏' : '显示' }}标签
                          </el-button>
                          <el-button
                            size="small"
                            @click="downloadRenderedImage"
                            :icon="Download"
                          >
                            下载图片
                          </el-button>
                        </el-space>
                      </div>
                      <div class="canvas-container">
                        <canvas
                          ref="renderCanvas"
                          class="render-canvas"
                          @click="handleCanvasClick"
                        />
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- 错误信息 -->
            <div v-if="testResult.error" class="error-section">
              <h4>错误信息</h4>
              <el-alert
                :title="testResult.error"
                type="error"
                show-icon
                :closable="false"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Connection, Lightning, Delete, Document, Download } from '@element-plus/icons-vue'
import { testingAPI } from '@/api'

// 响应式数据
const testingContainers = ref([])
const availableEndpoints = ref([])
const loadingEndpoints = ref(false)
const testForm = ref({
  containerId: '',
  endpoint: '',
  method: 'POST',
  file: null,
  parameters: ''
})
const testResult = ref(null)
const testing = ref(false)
const activeTab = ref('formatted')
const uploadRef = ref()

// 文件预览相关
const filePreview = ref(null)
const renderCanvas = ref(null)
const showDetectionBoxes = ref(true)
const showLabels = ref(true)

// 计算属性
const selectedContainer = computed(() => {
  return testingContainers.value.find(c => c.id === testForm.value.containerId)
})

const currentEndpoint = computed(() => {
  return availableEndpoints.value.find(e => e.path === testForm.value.endpoint)
})

const canQuickTest = computed(() => {
  return testForm.value.containerId && testForm.value.endpoint && testForm.value.file
})

const shouldShowImageRender = computed(() => {
  return testResult.value && filePreview.value && filePreview.value.type === 'image'
})

// 方法
const loadTestingContainers = async () => {
  try {
    const response = await testingAPI.getTestingContainers()
    if (response.success) {
      testingContainers.value = response.data.containers
    }
  } catch (error) {
    ElMessage.error('获取可测试容器失败')
  }
}

const onContainerChange = async () => {
  // 清空之前的数据
  testResult.value = null
  testForm.value.endpoint = ''
  testForm.value.method = 'POST'
  testForm.value.parameters = ''
  availableEndpoints.value = []

  const container = selectedContainer.value
  if (!container) return

  // 加载容器的API端点
  await loadContainerEndpoints(container.name)
}

const loadContainerEndpoints = async (containerName) => {
  if (!containerName) return

  loadingEndpoints.value = true
  try {
    const response = await testingAPI.getContainerEndpoints(containerName)
    if (response.success) {
      availableEndpoints.value = response.data.endpoints
      // 自动选择第一个端点
      if (availableEndpoints.value.length > 0) {
        const firstEndpoint = availableEndpoints.value[0]
        testForm.value.endpoint = firstEndpoint.path
        testForm.value.method = firstEndpoint.method
        onEndpointChange()
      }
    }
  } catch (error) {
    ElMessage.error('获取API端点失败')
  } finally {
    loadingEndpoints.value = false
  }
}

const onEndpointChange = () => {
  const endpoint = currentEndpoint.value
  if (endpoint) {
    testForm.value.method = endpoint.method
    // 根据端点类型填充默认参数
    fillDefaultParameters()
  }
}

const getMethodTagType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

const getParameterPlaceholder = () => {
  const endpoint = currentEndpoint.value
  if (!endpoint || !endpoint.parameters) {
    return '{"param1": "value1", "param2": "value2"}'
  }

  const example = {}
  Object.keys(endpoint.parameters).forEach(param => {
    if (param === 'file') return // 文件参数不在JSON中
    if (param.includes('threshold')) {
      example[param] = 0.5
    } else {
      example[param] = `value_${param}`
    }
  })

  return Object.keys(example).length > 0 ? JSON.stringify(example, null, 2) : '{}'
}

const fillDefaultParameters = () => {
  const endpoint = currentEndpoint.value
  if (!endpoint) {
    testForm.value.parameters = '{}'
    return
  }

  const defaultParams = {}

  // 处理新的API端点数据结构
  if (endpoint.default_params && Object.keys(endpoint.default_params).length > 0) {
    // 使用从OpenAPI文档解析的默认参数
    Object.assign(defaultParams, endpoint.default_params)
  } else if (endpoint.parameters && Array.isArray(endpoint.parameters)) {
    // 处理参数数组格式
    endpoint.parameters.forEach(param => {
      if (param.name === 'file') return // 文件参数不在JSON中
      if (param.in === 'formData' && param.name !== 'file') {
        if (param.default !== undefined) {
          defaultParams[param.name] = param.default
        } else if (param.type === 'boolean') {
          defaultParams[param.name] = false
        } else if (param.type === 'number' || param.type === 'integer') {
          if (param.name.includes('threshold')) {
            defaultParams[param.name] = 0.5
          } else {
            defaultParams[param.name] = 0
          }
        } else {
          defaultParams[param.name] = ''
        }
      }
    })
  } else if (endpoint.parameters && typeof endpoint.parameters === 'object') {
    // 处理旧的参数对象格式（向后兼容）
    Object.keys(endpoint.parameters).forEach(param => {
      if (param === 'file') return // 文件参数不在JSON中
      if (param === 'conf_threshold') {
        defaultParams[param] = 0.5
      } else if (param === 'iou_threshold') {
        defaultParams[param] = 0.4
      } else if (param.includes('threshold')) {
        defaultParams[param] = 0.5
      } else {
        defaultParams[param] = ''
      }
    })
  }

  testForm.value.parameters = JSON.stringify(defaultParams, null, 2)
}

const clearParameters = () => {
  testForm.value.parameters = '{}'
}

const quickTest = async () => {
  if (!canQuickTest.value) {
    ElMessage.warning('请选择容器、API端点和上传文件')
    return
  }

  // 快速测试：自动填充默认参数并执行
  const originalParams = testForm.value.parameters

  // 如果参数为空或只有空对象，自动填充默认参数
  if (!testForm.value.parameters || testForm.value.parameters.trim() === '' || testForm.value.parameters.trim() === '{}') {
    fillDefaultParameters()
  }

  // 执行测试
  await executeTest()

  ElMessage.success('快速测试完成！已自动使用默认参数')
}

const getRawJSON = (data) => {
  try {
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return String(data)
  }
}

const getDataStats = (data) => {
  try {
    if (!data || typeof data !== 'object') return null

    const stats = {}

    // 检测结果统计
    if (data.results || data.detections || data.predictions) {
      const results = data.results || data.detections || data.predictions
      if (Array.isArray(results)) {
        stats['🎯 检测数量'] = results.length

        // 统计置信度
        const confidences = results.map(r => r.confidence || r.score || 0).filter(c => c > 0)
        if (confidences.length > 0) {
          stats['📈 平均置信度'] = (confidences.reduce((a, b) => a + b, 0) / confidences.length).toFixed(3)
          stats['🔝 最高置信度'] = Math.max(...confidences).toFixed(3)
          stats['📉 最低置信度'] = Math.min(...confidences).toFixed(3)
        }

        // 统计类别
        const classes = results.map(r => r.class || r.label || r.category).filter(c => c)
        if (classes.length > 0) {
          const uniqueClasses = [...new Set(classes)]
          stats['🏷️ 检测类别'] = uniqueClasses.join(', ')
          stats['📊 类别数量'] = uniqueClasses.length
        }
      }
    }

    // 性能统计
    if (data.processing_time || data.inference_time) {
      stats['⏱️ 处理时间'] = `${data.processing_time || data.inference_time}ms`
    }

    // 图像信息
    if (data.image_width || data.width) {
      stats['🖼️ 图像尺寸'] = `${data.image_width || data.width} × ${data.image_height || data.height}`
    }

    // 模型信息
    if (data.model_name || data.algorithm_name) {
      stats['🤖 模型名称'] = data.model_name || data.algorithm_name
    }

    if (data.model_version || data.version) {
      stats['📦 模型版本'] = data.model_version || data.version
    }

    return Object.keys(stats).length > 0 ? stats : null
  } catch (error) {
    return null
  }
}

const handleFileChange = (file) => {
  testForm.value.file = file.raw

  // 创建文件预览
  createFilePreview(file.raw)
}

const createFilePreview = (file) => {
  if (!file) {
    filePreview.value = null
    return
  }

  const url = URL.createObjectURL(file)
  const fileType = getFileType(file.type)

  filePreview.value = {
    name: file.name,
    size: file.size,
    type: fileType,
    url: url,
    file: file
  }
}

const getFileType = (mimeType) => {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  return 'other'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const clearFile = () => {
  testForm.value.file = null
  if (filePreview.value) {
    URL.revokeObjectURL(filePreview.value.url)
    filePreview.value = null
  }
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const executeTest = async () => {
  if (!testForm.value.containerId) {
    ElMessage.warning('请选择要测试的容器')
    return
  }

  testing.value = true
  testResult.value = null

  try {
    const testData = {
      endpoint: testForm.value.endpoint,
      method: testForm.value.method,
      file: testForm.value.file,
      parameters: testForm.value.parameters || '{}'
    }

    const startTime = Date.now()
    const response = await testingAPI.testContainer(testForm.value.containerId, testData)
    const endTime = Date.now()

    testResult.value = {
      status: response.status || 200,
      data: response.data || response,
      response_time: endTime - startTime,
      error: null
    }

    ElMessage.success('测试执行成功')

    // 如果有图片和检测结果，渲染图片
    if (shouldShowImageRender.value) {
      await nextTick()
      renderImageWithDetections()
    }
  } catch (error) {
    const endTime = Date.now()
    testResult.value = {
      status: error.response?.status || 500,
      data: error.response?.data || null,
      response_time: endTime - Date.now(),
      error: error.message || '测试执行失败'
    }
    ElMessage.error('测试执行失败')
  } finally {
    testing.value = false
  }
}

const clearResult = () => {
  testResult.value = null
  activeTab.value = 'formatted'
}

const formatJSON = (data) => {
  try {
    if (typeof data === 'string') {
      // 尝试解析字符串为JSON
      try {
        const parsed = JSON.parse(data)
        return formatJSON(parsed) // 递归处理解析后的数据
      } catch {
        return data
      }
    }

    // 如果是对象，进行美化格式化
    if (typeof data === 'object' && data !== null) {

      // 处理测试响应的外层结构
      if (data.response && typeof data.response === 'object') {
        // 这是测试API返回的结构，提取response部分进行格式化
        const response = data.response

        if (response.success !== undefined && response.data) {
          const formatted = {
            '✅ 响应状态': response.success ? '成功' : '失败',
            '📝 响应消息': response.message || '无消息'
          }

          // 处理不同类型的data内容
          if (response.data.results || response.data.detections || response.data.predictions) {
            // 检测结果
            const results = response.data.results || response.data.detections || response.data.predictions
            formatted['📊 检测结果'] = results
            formatted['🎯 检测数量'] = Array.isArray(results) ? results.length : '未知'
          } else if (response.data.name || response.data.version) {
            // 算法信息
            formatted['🤖 算法名称'] = response.data.name || '未知'
            formatted['📦 版本'] = response.data.version || '未知'
            formatted['📄 描述'] = response.data.description || '无描述'
            if (response.data.capabilities) {
              formatted['🛠️ 功能'] = response.data.capabilities
            }
            if (response.data.classes) {
              formatted['🏷️ 支持类别'] = response.data.classes
            }
            if (response.data.supported_formats) {
              formatted['📁 支持格式'] = response.data.supported_formats
            }
          } else if (response.data.service) {
            // 健康检查
            formatted['🏥 服务名称'] = response.data.service
            formatted['💚 健康状态'] = response.data.status || '未知'
            if (response.data.engine_loaded !== undefined) {
              formatted['🔧 引擎状态'] = response.data.engine_loaded ? '已加载' : '未加载'
            }
          } else {
            // 其他数据类型
            formatted['📋 响应数据'] = response.data
          }

          // 添加时间信息
          if (response.timestamp) {
            formatted['🕐 响应时间'] = response.timestamp
          }
          if (response.processing_time) {
            formatted['⏱️ 处理时间'] = `${response.processing_time}ms`
          }

          // 添加错误信息
          if (response.error) {
            formatted['❌ 错误信息'] = response.error
          }

          // 添加请求信息
          if (data.container) {
            formatted['🐳 容器ID'] = data.container
          }
          if (data.endpoint) {
            formatted['🔗 API端点'] = data.endpoint
          }
          if (data.method) {
            formatted['📡 请求方法'] = data.method
          }
          if (data.status_code) {
            formatted['📊 状态码'] = data.status_code
          }

          return JSON.stringify(formatted, null, 2)
        }
      }

      // 处理标准API响应格式
      if (data.success !== undefined && data.data) {
        const formatted = {
          '✅ 响应状态': data.success ? '成功' : '失败',
          '📝 响应消息': data.message || '无消息'
        }

        // 处理不同类型的data内容
        if (data.data.results || data.data.detections || data.data.predictions) {
          // 检测结果
          const results = data.data.results || data.data.detections || data.data.predictions
          formatted['📊 检测结果'] = results
          formatted['🎯 检测数量'] = Array.isArray(results) ? results.length : '未知'
        } else if (data.data.name || data.data.version) {
          // 算法信息
          formatted['🤖 算法名称'] = data.data.name || '未知'
          formatted['📦 版本'] = data.data.version || '未知'
          formatted['📄 描述'] = data.data.description || '无描述'
          if (data.data.capabilities) {
            formatted['🛠️ 功能'] = data.data.capabilities
          }
          if (data.data.classes) {
            formatted['🏷️ 支持类别'] = data.data.classes
          }
          if (data.data.supported_formats) {
            formatted['📁 支持格式'] = data.data.supported_formats
          }
        } else if (data.data.service) {
          // 健康检查
          formatted['🏥 服务名称'] = data.data.service
          formatted['💚 健康状态'] = data.data.status || '未知'
          if (data.data.engine_loaded !== undefined) {
            formatted['🔧 引擎状态'] = data.data.engine_loaded ? '已加载' : '未加载'
          }
        } else {
          // 其他数据类型
          formatted['📋 响应数据'] = data.data
        }

        // 添加时间信息
        if (data.timestamp) {
          formatted['🕐 响应时间'] = data.timestamp
        }
        if (data.processing_time) {
          formatted['⏱️ 处理时间'] = `${data.processing_time}ms`
        }

        // 添加错误信息
        if (data.error) {
          formatted['❌ 错误信息'] = data.error
        }

        return JSON.stringify(formatted, null, 2)
      }

      // 直接的检测结果数据
      if (data.results || data.detections || data.predictions) {
        const formatted = {
          '📊 检测结果': data.results || data.detections || data.predictions,
          '⏱️ 处理时间': data.processing_time || data.inference_time || '未设置',
          '🎯 置信度阈值': data.conf_threshold || data.confidence_threshold || '未设置',
          '📏 IoU阈值': data.iou_threshold || '未设置',
          '🖼️ 图像信息': {
            '宽度': data.image_width || data.width || '未知',
            '高度': data.image_height || data.height || '未知',
            '格式': data.image_format || data.format || '未知'
          }
        }

        // 移除空值
        Object.keys(formatted).forEach(key => {
          if (formatted[key] === '未知' || formatted[key] === '未设置') {
            delete formatted[key]
          }
          if (typeof formatted[key] === 'object' && formatted[key] !== null) {
            Object.keys(formatted[key]).forEach(subKey => {
              if (formatted[key][subKey] === '未知' || formatted[key][subKey] === '未设置') {
                delete formatted[key][subKey]
              }
            })
            if (Object.keys(formatted[key]).length === 0) {
              delete formatted[key]
            }
          }
        })

        return JSON.stringify(formatted, null, 2)
      }

      // 默认格式化
      return JSON.stringify(data, null, 2)
    }

    return String(data)
  } catch (error) {
    return String(data)
  }
}

// 图片渲染相关方法
const renderImageWithDetections = async () => {
  if (!renderCanvas.value || !filePreview.value || !testResult.value) return

  const canvas = renderCanvas.value
  const ctx = canvas.getContext('2d')

  // 创建图片对象
  const img = new Image()
  img.onload = () => {
    // 设置canvas尺寸
    const maxWidth = 800
    const maxHeight = 600
    let { width, height } = img

    // 计算缩放比例
    const scale = Math.min(maxWidth / width, maxHeight / height, 1)
    width *= scale
    height *= scale

    canvas.width = width
    canvas.height = height

    // 绘制原图
    ctx.drawImage(img, 0, 0, width, height)

    // 绘制检测结果
    if (showDetectionBoxes.value) {
      drawDetections(ctx, width, height, img.width, img.height)
    }
  }
  img.src = filePreview.value.url
}

const drawDetections = (ctx, canvasWidth, canvasHeight, originalWidth, originalHeight) => {
  const detections = extractDetections(testResult.value.data)
  if (!detections || detections.length === 0) return

  const scaleX = canvasWidth / originalWidth
  const scaleY = canvasHeight / originalHeight

  detections.forEach((detection, index) => {
    const { bbox, class: className, class_name, label, confidence, score } = detection
    if (!bbox || bbox.length < 4) return

    // 获取类别名称，优先使用v2.0格式的label，然后是class_name，然后是class，最后是unknown
    const displayClassName = label || class_name || className || 'unknown'

    // 转换坐标
    const [x1, y1, x2, y2] = bbox
    const x = x1 * scaleX
    const y = y1 * scaleY
    const width = (x2 - x1) * scaleX
    const height = (y2 - y1) * scaleY

    // 绘制边界框
    ctx.strokeStyle = getColorForClass(displayClassName, index)
    ctx.lineWidth = 2
    ctx.strokeRect(x, y, width, height)

    // 绘制标签
    if (showLabels.value) {
      const label = `${displayClassName} ${((confidence || score || 0) * 100).toFixed(1)}%`
      const labelHeight = 20

      // 绘制标签背景
      ctx.fillStyle = getColorForClass(displayClassName, index)
      ctx.fillRect(x, y - labelHeight, ctx.measureText(label).width + 10, labelHeight)

      // 绘制标签文字
      ctx.fillStyle = 'white'
      ctx.font = '12px Arial'
      ctx.fillText(label, x + 5, y - 5)
    }
  })
}

const extractDetections = (responseData) => {
  try {
    console.log('提取检测结果，原始数据:', responseData)

    // v2.0统一响应格式：检查 data.detections
    if (responseData.data && responseData.data.detections) {
      console.log('找到v2.0格式检测结果:', responseData.data.detections)
      return responseData.data.detections
    }

    // 尝试从不同的响应格式中提取检测结果
    if (responseData.response && responseData.response.data) {
      const data = responseData.response.data
      return data.results || data.detections || data.predictions || []
    }

    if (responseData.results) return responseData.results
    if (responseData.detections) return responseData.detections
    if (responseData.predictions) return responseData.predictions

    console.log('未找到检测结果，返回空数组')
    return []
  } catch (error) {
    console.error('提取检测结果失败:', error)
    return []
  }
}

const getColorForClass = (className, index) => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]

  if (className) {
    // 基于类名生成一致的颜色
    let hash = 0
    for (let i = 0; i < className.length; i++) {
      hash = className.charCodeAt(i) + ((hash << 5) - hash)
    }
    return colors[Math.abs(hash) % colors.length]
  }

  return colors[index % colors.length]
}

const toggleDetectionBoxes = () => {
  showDetectionBoxes.value = !showDetectionBoxes.value
  renderImageWithDetections()
}

const toggleLabels = () => {
  showLabels.value = !showLabels.value
  renderImageWithDetections()
}

const downloadRenderedImage = () => {
  if (!renderCanvas.value) return

  const link = document.createElement('a')
  link.download = `rendered_${filePreview.value?.name || 'image'}.png`
  link.href = renderCanvas.value.toDataURL()
  link.click()
}

const handleCanvasClick = (event) => {
  // 可以添加点击检测框的交互功能
  console.log('Canvas clicked at:', event.offsetX, event.offsetY)
}

// 生命周期
onMounted(async () => {
  await loadTestingContainers()

  // 监听全局刷新事件
  window.addEventListener('refresh-data', loadTestingContainers)
})
</script>

<style scoped>
.empty-result {
  text-align: center;
  padding: 40px 0;
}

.testing-status {
  text-align: center;
  padding: 20px 0;
}

.testing-text {
  margin-top: 16px;
  color: #409EFF;
  font-size: 14px;
}

.result-content {
  padding: 0;
}

.response-section {
  margin-top: 20px;
}

.response-section h4 {
  margin-bottom: 12px;
  color: #303133;
}

.error-section {
  margin-top: 20px;
}

.error-section h4 {
  margin-bottom: 12px;
  color: #F56C6C;
}

.json-content {
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.parameter-hints {
  margin-bottom: 10px;
}

.parameter-hints ul {
  margin: 0;
  padding-left: 20px;
}

.parameter-hints li {
  margin-bottom: 5px;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
  line-height: 1.4;
}

:deep(.el-upload__tip) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.test-buttons {
  display: flex;
  align-items: center;
  width: 100%;
}

.button-tips {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.file-uploaded {
  color: #67c23a;
  font-weight: 500;
  margin-left: 10px;
}

.formatted-content {
  position: relative;
}

.raw-content {
  position: relative;
}

.raw-json {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  color: #666;
}

.stats-content {
  padding: 10px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 文件上传和预览样式 */
.upload-section {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.upload-area {
  flex: 1;
}

.file-preview {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.preview-title {
  font-weight: 500;
  color: #303133;
}

.preview-content {
  padding: 15px;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  object-fit: contain;
}

.preview-video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 32px;
  color: #909399;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

/* 图片渲染样式 */
.render-content {
  padding: 20px 0;
}

.no-image-tip {
  text-align: center;
  padding: 40px 0;
}

.image-render-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.render-controls {
  display: flex;
  justify-content: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.canvas-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.render-canvas {
  max-width: 100%;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: crosshair;
}
</style>
