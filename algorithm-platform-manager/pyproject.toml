[project]
name = "algorithm-platform-manager"
version = "1.0.0"
description = "AI算法容器管理平台"
authors = [
    {name = "Algorithm Platform Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
requires-python = ">=3.8.1"
keywords = ["algorithm", "docker", "management", "platform", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Systems Administration",
]

dependencies = [
    # Web框架
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",

    # HTTP客户端
    "aiohttp>=3.9.1",
    "httpx>=0.25.0",
    "httpx>=0.25.2",

    # Docker客户端
    "docker>=6.1.3",

    # 系统监控
    "psutil>=5.9.6",

    # 数据处理
    "pydantic>=2.5.0",

    # 文件处理
    "python-multipart>=0.0.6",

    # 日志
    "loguru>=0.7.2",

    # 缓存和会话
    "redis>=5.0.1",
    "aioredis>=2.0.1",

    # 配置管理
    "pydantic-settings>=2.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[project.urls]
Homepage = "https://github.com/algorithm-platform/manager"
Repository = "https://github.com/algorithm-platform/manager.git"
Documentation = "https://algorithm-platform.readthedocs.io"
"Bug Tracker" = "https://github.com/algorithm-platform/manager/issues"

[project.scripts]
algorithm-platform = "src.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
