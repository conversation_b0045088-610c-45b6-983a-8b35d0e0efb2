#!/usr/bin/env python3
"""
在线测试API路由
提供算法容器的在线测试功能
"""

import logging
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request, File, UploadFile, Form
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TestRequest(BaseModel):
    """测试请求模型"""
    container_name: str
    endpoint: str = "/api/v1/detect"
    method: str = "POST"
    parameters: Dict[str, Any] = {}


@router.get("/containers", response_model=APIResponse)
async def get_testable_containers(request: Request):
    """获取可测试的容器列表"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 筛选运行中的容器
        running_containers = []
        for container in containers:
            if container.get("status") == "running" and container.get("ports"):
                # 获取API端口
                ports = container.get("ports", {})
                api_port = None
                for container_port, host_port in ports.items():
                    if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                        api_port = host_port
                        break
                
                if api_port:
                    # 正确获取标签信息
                    labels = container.get("labels", {})
                    algorithm_name = labels.get("algorithm.name", "未知算法")
                    algorithm_type = labels.get("algorithm.type", "未知类型")

                    running_containers.append({
                        "name": container["name"],
                        "id": container["id"],
                        "algorithm_name": algorithm_name,
                        "algorithm_type": algorithm_type,
                        "api_port": api_port,
                        "api_url": f"http://localhost:{api_port}",
                        "health": container.get("health", {})
                    })
        
        return APIResponse(
            success=True,
            message=f"发现 {len(running_containers)} 个可测试容器",
            data={
                "containers": running_containers,
                "total": len(running_containers)
            }
        )
        
    except Exception as e:
        logger.error(f"获取可测试容器失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取容器列表失败: {str(e)}")


@router.get("/containers/{container_name}/info", response_model=APIResponse)
async def get_container_api_info(container_name: str, request: Request):
    """获取容器的API信息"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        if container.get("status") != "running":
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 未运行")
        
        # 获取API端口
        ports = container.get("ports", {})
        api_port = None
        for container_port, host_port in ports.items():
            if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                api_port = host_port
                break
        
        if not api_port:
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 没有暴露API端口")
        
        api_url = f"http://localhost:{api_port}"
        
        # 尝试获取API信息
        try:
            async with aiohttp.ClientSession() as session:
                # 尝试获取健康检查
                async with session.get(f"{api_url}/api/v1/health", timeout=5) as resp:
                    health_data = await resp.json() if resp.status == 200 else None
                
                # 尝试获取算法信息
                async with session.get(f"{api_url}/api/v1/info", timeout=5) as resp:
                    info_data = await resp.json() if resp.status == 200 else None
                
                # 尝试获取API文档
                docs_available = False
                try:
                    async with session.get(f"{api_url}/docs", timeout=5) as resp:
                        docs_available = resp.status == 200
                except:
                    pass
        
        except Exception as e:
            logger.warning(f"无法连接到容器API: {e}")
            health_data = None
            info_data = None
            docs_available = False
        
        return APIResponse(
            success=True,
            message="容器API信息获取成功",
            data={
                "container": container_name,
                "api_url": api_url,
                "api_port": api_port,
                "health": health_data,
                "info": info_data,
                "docs_available": docs_available,
                "docs_url": f"{api_url}/docs" if docs_available else None,
                "common_endpoints": [
                    "/api/v1/health",
                    "/api/v1/info", 
                    "/api/v1/detect",
                    "/docs"
                ]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取容器API信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取API信息失败: {str(e)}")


@router.get("/containers/{container_name}/endpoints", response_model=APIResponse)
async def get_container_endpoints(container_name: str, request: Request):
    """获取容器的详细API端点信息"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()

        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break

        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")

        if container.get("status") != "running":
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 未运行")

        # 获取API端口
        ports = container.get("ports", {})
        api_port = None
        for container_port, host_port in ports.items():
            if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                api_port = host_port
                break

        if not api_port:
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 没有暴露API端口")

        api_url = f"http://localhost:{api_port}"

        # 尝试获取OpenAPI文档
        endpoints = []
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{api_url}/openapi.json", timeout=10) as resp:
                    if resp.status == 200:
                        openapi_data = await resp.json()

                        # 解析OpenAPI文档
                        paths = openapi_data.get("paths", {})
                        components = openapi_data.get("components", {})
                        schemas = components.get("schemas", {})

                        def resolve_ref(ref_path, schemas):
                            """解析$ref引用"""
                            if ref_path.startswith("#/components/schemas/"):
                                schema_name = ref_path.replace("#/components/schemas/", "")
                                return schemas.get(schema_name, {})
                            return {}

                        for path, methods in paths.items():
                            for method, details in methods.items():
                                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                                    # 解析参数
                                    parameters = []
                                    default_params = {}

                                    # 处理路径参数
                                    if "parameters" in details:
                                        for param in details["parameters"]:
                                            param_info = {
                                                "name": param.get("name"),
                                                "type": param.get("schema", {}).get("type", "string"),
                                                "required": param.get("required", False),
                                                "description": param.get("description", ""),
                                                "in": param.get("in", "query")
                                            }
                                            if "default" in param.get("schema", {}):
                                                param_info["default"] = param["schema"]["default"]
                                                default_params[param["name"]] = param["schema"]["default"]
                                            parameters.append(param_info)

                                    # 处理请求体参数（表单数据）
                                    if "requestBody" in details:
                                        request_body = details["requestBody"]
                                        content = request_body.get("content", {})

                                        # 处理multipart/form-data
                                        if "multipart/form-data" in content:
                                            schema = content["multipart/form-data"].get("schema", {})

                                            # 处理$ref引用
                                            if "$ref" in schema:
                                                schema = resolve_ref(schema["$ref"], schemas)

                                            properties = schema.get("properties", {})
                                            required_fields = schema.get("required", [])

                                            for prop_name, prop_details in properties.items():
                                                if prop_name != "file":  # 文件参数特殊处理
                                                    param_info = {
                                                        "name": prop_name,
                                                        "type": prop_details.get("type", "string"),
                                                        "required": prop_name in required_fields,
                                                        "description": prop_details.get("description", ""),
                                                        "in": "formData"
                                                    }
                                                    if "default" in prop_details:
                                                        param_info["default"] = prop_details["default"]
                                                        default_params[prop_name] = prop_details["default"]
                                                    parameters.append(param_info)

                                    endpoint_info = {
                                        "path": path,
                                        "method": method.upper(),
                                        "summary": details.get("summary", ""),
                                        "description": details.get("description", ""),
                                        "parameters": parameters,
                                        "default_params": default_params,
                                        "requires_file": any(p.get("name") == "file" for p in parameters) or
                                                       "multipart/form-data" in details.get("requestBody", {}).get("content", {}),
                                        "tags": details.get("tags", [])
                                    }
                                    endpoints.append(endpoint_info)
                    else:
                        # 如果无法获取OpenAPI文档，返回基础端点
                        endpoints = [
                            {
                                "path": "/api/v1/health",
                                "method": "GET",
                                "summary": "Health Check",
                                "description": "检查服务健康状态",
                                "parameters": [],
                                "default_params": {},
                                "requires_file": False,
                                "tags": ["health"]
                            },
                            {
                                "path": "/api/v1/info",
                                "method": "GET",
                                "summary": "Get Info",
                                "description": "获取算法信息",
                                "parameters": [],
                                "default_params": {},
                                "requires_file": False,
                                "tags": ["info"]
                            },
                            {
                                "path": "/api/v1/detect",
                                "method": "POST",
                                "summary": "Detect",
                                "description": "执行检测",
                                "parameters": [],
                                "default_params": {},
                                "requires_file": True,
                                "tags": ["detection"]
                            }
                        ]
        except Exception as e:
            logger.warning(f"无法获取OpenAPI文档: {e}")
            # 返回基础端点
            endpoints = [
                {
                    "path": "/api/v1/health",
                    "method": "GET",
                    "summary": "Health Check",
                    "description": "检查服务健康状态",
                    "parameters": [],
                    "default_params": {},
                    "requires_file": False,
                    "tags": ["health"]
                },
                {
                    "path": "/api/v1/info",
                    "method": "GET",
                    "summary": "Get Info",
                    "description": "获取算法信息",
                    "parameters": [],
                    "default_params": {},
                    "requires_file": False,
                    "tags": ["info"]
                },
                {
                    "path": "/api/v1/detect",
                    "method": "POST",
                    "summary": "Detect",
                    "description": "执行检测",
                    "parameters": [],
                    "default_params": {},
                    "requires_file": True,
                    "tags": ["detection"]
                }
            ]

        return APIResponse(
            success=True,
            message="API端点信息获取成功",
            data={
                "container": container_name,
                "api_url": api_url,
                "endpoints": endpoints,
                "total_endpoints": len(endpoints)
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取API端点信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取端点信息失败: {str(e)}")


@router.post("/containers/{container_name}/test", response_model=APIResponse)
async def test_container_api(
    container_name: str,
    request: Request,
    file: UploadFile = File(None),
    endpoint: str = Form(default="/api/v1/detect"),
    method: str = Form(default="POST"),
    parameters: str = Form(default="{}")
):
    """测试容器API"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        if container.get("status") != "running":
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 未运行")
        
        # 获取API端口
        ports = container.get("ports", {})
        api_port = None
        for container_port, host_port in ports.items():
            if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                api_port = host_port
                break
        
        if not api_port:
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 没有暴露API端口")
        
        api_url = f"http://localhost:{api_port}{endpoint}"
        
        # 解析参数
        import json
        try:
            params = json.loads(parameters) if parameters != "{}" else {}
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="参数格式错误，请使用有效的JSON格式")
        
        # 执行API调用
        try:
            async with aiohttp.ClientSession() as session:
                if method.upper() == "GET":
                    async with session.get(api_url, params=params, timeout=30) as resp:
                        response_data = await resp.json()
                        status_code = resp.status
                
                elif method.upper() == "POST":
                    if file:
                        # 文件上传
                        file_content = await file.read()
                        data = aiohttp.FormData()

                        # 设置正确的content_type
                        content_type = file.content_type or 'application/octet-stream'
                        # 如果没有content_type，根据文件扩展名推断
                        if content_type == 'application/octet-stream' and file.filename:
                            ext = file.filename.lower().split('.')[-1]
                            if ext in ['jpg', 'jpeg']:
                                content_type = 'image/jpeg'
                            elif ext == 'png':
                                content_type = 'image/png'
                            elif ext == 'bmp':
                                content_type = 'image/bmp'
                            elif ext in ['tiff', 'tif']:
                                content_type = 'image/tiff'
                            elif ext == 'webp':
                                content_type = 'image/webp'

                        data.add_field('file', file_content, filename=file.filename, content_type=content_type)

                        # 添加其他参数
                        for key, value in params.items():
                            data.add_field(key, str(value))
                        
                        async with session.post(api_url, data=data, timeout=30) as resp:
                            response_data = await resp.json()
                            status_code = resp.status
                    else:
                        # JSON数据
                        async with session.post(api_url, json=params, timeout=30) as resp:
                            response_data = await resp.json()
                            status_code = resp.status
                else:
                    raise HTTPException(status_code=400, detail=f"不支持的HTTP方法: {method}")
        
        except asyncio.TimeoutError:
            raise HTTPException(status_code=408, detail="API调用超时")
        except aiohttp.ClientError as e:
            raise HTTPException(status_code=500, detail=f"API调用失败: {str(e)}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"测试执行失败: {str(e)}")
        
        return APIResponse(
            success=True,
            message=f"API测试完成，状态码: {status_code}",
            data={
                "container": container_name,
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response": response_data,
                "request_info": {
                    "url": api_url,
                    "parameters": params,
                    "has_file": file is not None,
                    "file_name": file.filename if file else None
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")


@router.get("/containers/{container_name}/endpoints", response_model=APIResponse)
async def get_container_endpoints(container_name: str, request: Request):
    """获取容器的可用API端点"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        # 获取API端口
        ports = container.get("ports", {})
        api_port = None
        for container_port, host_port in ports.items():
            if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                api_port = host_port
                break
        
        if not api_port:
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 没有暴露API端口")
        
        api_url = f"http://localhost:{api_port}"
        
        # 尝试从算法容器的OpenAPI文档动态获取端点
        endpoints = []
        try:
            import httpx
            async with httpx.AsyncClient(timeout=5.0) as client:
                # 尝试获取OpenAPI文档
                openapi_response = await client.get(f"{api_url}/openapi.json")
                if openapi_response.status_code == 200:
                    openapi_data = openapi_response.json()
                    paths = openapi_data.get("paths", {})

                    for path, methods in paths.items():
                        for method, details in methods.items():
                            if method.upper() in ["GET", "POST", "PUT", "DELETE"]:
                                # 提取参数信息
                                parameters = {}
                                if "requestBody" in details:
                                    # 处理POST请求的参数
                                    request_body = details["requestBody"]
                                    content = request_body.get("content", {})
                                    if "multipart/form-data" in content:
                                        schema = content["multipart/form-data"].get("schema", {})
                                        properties = schema.get("properties", {})
                                        for prop_name, prop_details in properties.items():
                                            prop_type = prop_details.get("type", "string")
                                            prop_desc = prop_details.get("description", f"{prop_name}参数")
                                            if prop_type == "string" and prop_details.get("format") == "binary":
                                                parameters[prop_name] = "上传文件"
                                            else:
                                                parameters[prop_name] = prop_desc

                                if "parameters" in details:
                                    # 处理查询参数
                                    for param in details["parameters"]:
                                        param_name = param.get("name", "")
                                        param_desc = param.get("description", f"{param_name}参数")
                                        parameters[param_name] = param_desc

                                endpoints.append({
                                    "path": path,
                                    "method": method.upper(),
                                    "description": details.get("summary", details.get("description", "API端点")),
                                    "parameters": parameters
                                })

        except Exception as e:
            logger.warning(f"无法获取容器 {container_name} 的OpenAPI文档: {e}")

        # 如果动态获取失败，使用预定义的基础端点
        if not endpoints:
            endpoints = [
                {
                    "path": "/api/v1/health",
                    "method": "GET",
                    "description": "健康检查",
                    "parameters": {}
                },
                {
                    "path": "/api/v1/info",
                    "method": "GET",
                    "description": "算法信息",
                    "parameters": {}
                },
                {
                    "path": "/api/v1/detect",
                    "method": "POST",
                    "description": "目标检测/算法推理",
                    "parameters": {
                        "file": "上传文件",
                        "conf_threshold": "置信度阈值(可选)",
                        "iou_threshold": "IoU阈值(可选)"
                    }
                }
            ]
        
        return APIResponse(
            success=True,
            message=f"端点信息获取成功，发现 {len(endpoints)} 个API端点",
            data={
                "container": container_name,
                "api_url": api_url,
                "endpoints": endpoints,
                "docs_url": f"{api_url}/docs",
                "total_endpoints": len(endpoints),
                "discovery_method": "openapi" if len(endpoints) > 3 else "predefined"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取端点信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取端点失败: {str(e)}")
