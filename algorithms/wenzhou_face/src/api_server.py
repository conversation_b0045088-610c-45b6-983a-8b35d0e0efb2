#!/usr/bin/env python3
"""
温州人脸识别算法 - API服务器 (v2.0统一响应格式)
提供RESTful API接口用于人脸检测、识别、比对和质量评估
"""

import os
import io
import json
import time
from typing import List, Optional
from pathlib import Path
from datetime import datetime

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image

# 导入推理引擎
from inference_engine import WenzhouFaceEngine
from logger_config import get_logger

# 导入统一响应模型
from unified_models import (
    UnifiedResponse,
    DetectionData,
    FaceComparisonData,
    Metadata,
    ModelInfo,
    HardwareInfo,
    create_success_response,
    create_error_response,
    convert_face_detection_to_v2
)

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="温州人脸识别算法API v2.0",
    description="基于深度学习的人脸检测、识别、比对和质量评估API服务 - 统一响应格式v2.0",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[WenzhouFaceEngine] = None


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化温州人脸识别引擎...")
        config_path = "config.ini"
        engine = WenzhouFaceEngine(config_path)
        logger.info("温州人脸识别引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health", response_model=UnifiedResponse)
async def health_check():
    """健康检查接口 - v2.0格式"""
    start_time = time.time()

    health_data = {
        "service": "温州人脸识别算法",
        "status": "healthy",
        "engine_loaded": engine is not None
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="wenzhou_face",
            version="2.0.0"
        ),
        hardware_info=HardwareInfo(
            device="cpu"
        )
    )

    return create_success_response(
        data=health_data,
        metadata=metadata
    )


@app.get("/api/v1/info", response_model=UnifiedResponse)
async def get_algorithm_info():
    """获取算法信息 - v2.0格式"""
    start_time = time.time()

    algorithm_info = {
        "algorithm_name": "温州人脸识别算法",
        "algorithm_version": "2.0.0",
        "algorithm_type": "人脸识别",
        "description": "基于深度学习的人脸检测、识别、比对和质量评估算法",
        "capabilities": {
            "input_modes": ["file", "batch"],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"],
            "max_file_size_mb": 50,
            "concurrent_requests": 3
        },
        "model_info": {
            "framework": "ONNX",
            "detection_model": "SCRFD",
            "feature_model": "ResNet50",
            "landmark_model": "PFLD",
            "input_size": [640, 640]
        },
        "features": [
            "人脸检测",
            "人脸特征提取",
            "人脸质量评估",
            "人脸比对",
            "关键点检测"
        ]
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="wenzhou_face",
            version="2.0.0"
        )
    )

    return create_success_response(
        data=algorithm_info,
        metadata=metadata
    )


@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect_faces(
    file: UploadFile = File(...),
    extract_features: bool = Form(default=False),
    assess_quality: bool = Form(default=False)
):
    """人脸检测接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 验证文件类型
        if not file.content_type.startswith('image/'):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件格式",
                error_details="仅支持图片文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))

        # 转换为numpy数组
        image_array = np.array(image)

        # 执行推理
        legacy_results = engine.process_image(
            image_array,
            extract_features=extract_features,
            assess_quality=assess_quality
        )

        # 转换为v2.0格式
        detection_data = convert_face_detection_to_v2(legacy_results)

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=legacy_results.get('image_shape', [image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3]),
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="wenzhou_face",
                version="2.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=detection_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"人脸检测失败: {e}")
        return create_error_response(
            error_code="DETECTION_ERROR",
            error_message="人脸检测失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/compare", response_model=UnifiedResponse)
async def compare_faces(
    file1: UploadFile = File(...),
    file2: UploadFile = File(...)
):
    """人脸比对接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取两张图像
        image1_data = await file1.read()
        image2_data = await file2.read()

        image1 = np.array(Image.open(io.BytesIO(image1_data)))
        image2 = np.array(Image.open(io.BytesIO(image2_data)))

        # 提取特征
        results1 = engine.process_image(image1, extract_features=True)
        results2 = engine.process_image(image2, extract_features=True)

        if results1['num_faces'] == 0:
            return create_error_response(
                error_code="NO_FACE_DETECTED",
                error_message="图像1中未检测到人脸",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        if results2['num_faces'] == 0:
            return create_error_response(
                error_code="NO_FACE_DETECTED",
                error_message="图像2中未检测到人脸",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 获取特征向量
        feature1 = np.array(results1['faces'][0]['feature'])
        feature2 = np.array(results2['faces'][0]['feature'])

        # 计算相似度
        similarity = engine.compare_faces(feature1, feature2)
        threshold = engine.config.getfloat('FACE_RECOGNITION', 'similarity_threshold', fallback=0.6)
        is_same_person = similarity >= threshold

        # 创建比对数据
        comparison_data = FaceComparisonData(
            similarity_score=float(similarity),
            is_same_person=is_same_person,
            threshold=threshold,
            face1_quality=results1['faces'][0].get('quality_score'),
            face2_quality=results2['faces'][0].get('quality_score')
        )

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="wenzhou_face",
                version="2.0.0"
            )
        )

        return create_success_response(
            data=comparison_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"人脸比对失败: {e}")
        return create_error_response(
            error_code="COMPARISON_ERROR",
            error_message="人脸比对失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/extract_features", response_model=UnifiedResponse)
async def extract_features(file: UploadFile = File(...)):
    """特征提取接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 验证文件类型
        if not file.content_type.startswith('image/'):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件格式",
                error_details="仅支持图片文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        image_array = np.array(image)

        # 执行特征提取
        legacy_results = engine.process_image(image_array, extract_features=True)

        if legacy_results['num_faces'] == 0:
            return create_error_response(
                error_code="NO_FACE_DETECTED",
                error_message="图像中未检测到人脸",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 提取特征数据
        features_data = {
            "num_faces": legacy_results['num_faces'],
            "features": []
        }

        for i, face in enumerate(legacy_results['faces']):
            feature_info = {
                "face_id": i,
                "bbox": face['bbox'],
                "confidence": face['confidence'],
                "feature_vector": face['feature'].tolist() if hasattr(face['feature'], 'tolist') else face['feature'],
                "feature_dimension": len(face['feature']),
                "landmarks": face.get('landmarks', []),
                "quality_score": face.get('quality_score')
            }
            features_data["features"].append(feature_info)

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=[image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="wenzhou_face",
                version="2.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=features_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"特征提取失败: {e}")
        return create_error_response(
            error_code="FEATURE_EXTRACTION_ERROR",
            error_message="特征提取失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/compare_multiple", response_model=UnifiedResponse)
async def compare_multiple_faces(files: List[UploadFile] = File(...)):
    """多人脸比对接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        if len(files) < 2:
            return create_error_response(
                error_code="INSUFFICIENT_FILES",
                error_message="至少需要2个文件进行比对",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        if len(files) > 10:  # 限制比对文件数量
            return create_error_response(
                error_code="TOO_MANY_FILES",
                error_message="比对文件数量过多",
                error_details="单次最多支持10个文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 提取所有图像的特征
        face_features = []
        file_info = []

        for i, file in enumerate(files):
            try:
                # 验证文件类型
                if not file.content_type.startswith('image/'):
                    continue

                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_array = np.array(image)

                # 提取特征
                results = engine.process_image(image_array, extract_features=True)

                if results['num_faces'] > 0:
                    face_features.append({
                        'file_index': i,
                        'filename': file.filename,
                        'feature': np.array(results['faces'][0]['feature']),
                        'confidence': results['faces'][0]['confidence'],
                        'quality_score': results['faces'][0].get('quality_score')
                    })
                    file_info.append({
                        'file_index': i,
                        'filename': file.filename,
                        'has_face': True,
                        'face_confidence': results['faces'][0]['confidence']
                    })
                else:
                    file_info.append({
                        'file_index': i,
                        'filename': file.filename,
                        'has_face': False,
                        'error': "未检测到人脸"
                    })

            except Exception as e:
                file_info.append({
                    'file_index': i,
                    'filename': file.filename,
                    'has_face': False,
                    'error': str(e)
                })

        if len(face_features) < 2:
            return create_error_response(
                error_code="INSUFFICIENT_FACES",
                error_message="至少需要2张包含人脸的图像进行比对",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 进行两两比对
        threshold = engine.config.getfloat('FACE_RECOGNITION', 'similarity_threshold', fallback=0.6)
        comparison_results = []

        for i in range(len(face_features)):
            for j in range(i + 1, len(face_features)):
                face1 = face_features[i]
                face2 = face_features[j]

                # 计算相似度
                similarity = engine.compare_faces(face1['feature'], face2['feature'])
                is_same_person = similarity >= threshold

                comparison_results.append({
                    'pair_id': f"{i}_{j}",
                    'file1_index': face1['file_index'],
                    'file1_name': face1['filename'],
                    'file2_index': face2['file_index'],
                    'file2_name': face2['filename'],
                    'similarity_score': float(similarity),
                    'is_same_person': is_same_person,
                    'threshold': threshold
                })

        # 创建比对数据
        multiple_comparison_data = {
            "total_files": len(files),
            "valid_faces": len(face_features),
            "total_comparisons": len(comparison_results),
            "threshold": threshold,
            "file_info": file_info,
            "comparison_results": comparison_results,
            "summary": {
                "same_person_pairs": len([r for r in comparison_results if r['is_same_person']]),
                "different_person_pairs": len([r for r in comparison_results if not r['is_same_person']]),
                "avg_similarity": sum([r['similarity_score'] for r in comparison_results]) / len(comparison_results) if comparison_results else 0,
                "max_similarity": max([r['similarity_score'] for r in comparison_results]) if comparison_results else 0,
                "min_similarity": min([r['similarity_score'] for r in comparison_results]) if comparison_results else 0
            }
        }

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="wenzhou_face",
                version="2.0.0"
            )
        )

        return create_success_response(
            data=multiple_comparison_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"多人脸比对失败: {e}")
        return create_error_response(
            error_code="MULTIPLE_COMPARISON_ERROR",
            error_message="多人脸比对失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.get("/api/v1/status", response_model=UnifiedResponse)
async def get_status():
    """获取运行状态 - v2.0格式"""
    start_time = time.time()

    status_data = {
        "status": "ready" if engine else "loading",
        "message": "模型已加载，准备接受任务" if engine else "模型加载中",
        "uptime_seconds": int(time.time() - start_time),
        "current_load": {
            "active_tasks": 0,
            "queue_length": 0,
            "cpu_percent": 0.0,
            "memory_used_mb": 0,
        },
        "statistics": {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_response_time_ms": 0
        }
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="wenzhou_face",
            version="2.0.0"
        )
    )

    return create_success_response(
        data=status_data,
        metadata=metadata
    )


@app.post("/api/v1/quality", response_model=UnifiedResponse)
async def assess_quality(file: UploadFile = File(...)):
    """人脸质量评估接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 验证文件类型
        if not file.content_type.startswith('image/'):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件格式",
                error_details="仅支持图片文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        image_array = np.array(image)

        # 执行质量评估
        legacy_results = engine.process_image(image_array, assess_quality=True)

        if legacy_results['num_faces'] == 0:
            return create_error_response(
                error_code="NO_FACE_DETECTED",
                error_message="图像中未检测到人脸",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 提取质量评估数据
        quality_data = {
            "num_faces": legacy_results['num_faces'],
            "quality_assessments": []
        }

        for i, face in enumerate(legacy_results['faces']):
            quality_info = {
                "face_id": i,
                "bbox": face['bbox'],
                "confidence": face['confidence'],
                "quality_score": face.get('quality_score', 0.0),
                "quality_details": face.get('quality_details', {}),
                "landmarks": face.get('landmarks', []),
                "pose_angles": face.get('pose_angles', {}),
                "is_good_quality": face.get('quality_score', 0.0) >= engine.config.getfloat('FACE_QUALITY', 'min_quality_score', fallback=0.5)
            }
            quality_data["quality_assessments"].append(quality_info)

        # 添加整体质量统计
        quality_scores = [face.get('quality_score', 0.0) for face in legacy_results['faces']]
        quality_data["summary"] = {
            "avg_quality": sum(quality_scores) / len(quality_scores) if quality_scores else 0,
            "min_quality": min(quality_scores) if quality_scores else 0,
            "max_quality": max(quality_scores) if quality_scores else 0,
            "good_quality_faces": len([q for q in quality_scores if q >= engine.config.getfloat('FACE_QUALITY', 'min_quality_score', fallback=0.5)]),
            "poor_quality_faces": len([q for q in quality_scores if q < engine.config.getfloat('FACE_QUALITY', 'min_quality_score', fallback=0.5)])
        }

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=[image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="wenzhou_face",
                version="2.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=quality_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"质量评估失败: {e}")
        return create_error_response(
            error_code="QUALITY_ASSESSMENT_ERROR",
            error_message="质量评估失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/batch_detect", response_model=UnifiedResponse)
async def batch_detect(
    files: List[UploadFile] = File(...),
    extract_features: bool = Form(default=True),
    assess_quality: bool = Form(default=False)
):
    """批量人脸检测接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        if len(files) > 50:  # 限制批量处理数量
            return create_error_response(
                error_code="TOO_MANY_FILES",
                error_message="批量处理文件数量过多",
                error_details="单次最多支持50个文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        batch_results = []
        total_faces = 0
        successful_files = 0
        failed_files = 0

        for i, file in enumerate(files):
            try:
                # 验证文件类型
                if not file.content_type.startswith('image/'):
                    batch_results.append({
                        'filename': file.filename,
                        'index': i,
                        'success': False,
                        'error': "不支持的文件格式",
                        'num_faces': 0,
                        'faces': []
                    })
                    failed_files += 1
                    continue

                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_array = np.array(image)

                # 执行检测
                legacy_result = engine.process_image(
                    image_array,
                    extract_features=extract_features,
                    assess_quality=assess_quality
                )

                # 转换为v2.0格式
                detection_data = convert_face_detection_to_v2(legacy_result)

                batch_results.append({
                    'filename': file.filename,
                    'index': i,
                    'success': True,
                    'num_faces': legacy_result['num_faces'],
                    'faces': [d.model_dump() for d in detection_data.detections],
                    'summary': detection_data.summary.model_dump()
                })

                total_faces += legacy_result['num_faces']
                successful_files += 1

            except Exception as e:
                batch_results.append({
                    'filename': file.filename,
                    'index': i,
                    'success': False,
                    'error': str(e),
                    'num_faces': 0,
                    'faces': []
                })
                failed_files += 1

        batch_data = {
            "batch_id": f"batch_{int(time.time())}",
            "total_files": len(files),
            "successful_files": successful_files,
            "failed_files": failed_files,
            "total_faces": total_faces,
            "results": batch_results,
            "summary": {
                "success_rate": successful_files / len(files) if files else 0,
                "avg_faces_per_file": total_faces / successful_files if successful_files > 0 else 0,
                "files_with_faces": len([r for r in batch_results if r.get('num_faces', 0) > 0]),
                "files_without_faces": len([r for r in batch_results if r.get('num_faces', 0) == 0 and r.get('success', False)])
            }
        }

        metadata = Metadata(
            processing_time_ms=(time.time() - start_time) * 1000,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="wenzhou_face",
                version="2.0.0"
            )
        )

        return create_success_response(
            data=batch_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"批量检测失败: {e}")
        return create_error_response(
            error_code="BATCH_DETECTION_ERROR",
            error_message="批量检测失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


if __name__ == "__main__":
    # 启动API服务器 - v2.0
    logger.info("启动温州人脸识别算法API服务器 v2.0")
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
